package com.hsbc.transactionmanagement.model;

import lombok.*;

import java.time.LocalDateTime;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Transaction {

    private Long id;
    private double amount;
    private String currency;   // enum
    private LocalDateTime timestamp;
    private String description;
    private String status; // enum 交易状态 (例如: "PENDING", "COMPLETED", "FAILED", "CANCELLED")
    private Long senderAccountId;    // 发送方账户ID
    private Long receiverAccountId;  // 接收方账户ID
    private String referenceNumber;  // 参考号 (例如: 支付单号, 银行参考号) - 可以作为外部系统的关联

}
