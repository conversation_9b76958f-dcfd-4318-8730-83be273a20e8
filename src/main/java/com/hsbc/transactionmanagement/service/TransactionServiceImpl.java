package com.hsbc.transactionmanagement.service;

import com.hsbc.transactionmanagement.exceptions.DuplicateTransactionException;
import com.hsbc.transactionmanagement.exceptions.TransactionNotFoundException;
import com.hsbc.transactionmanagement.model.Transaction;
import org.springframework.stereotype.Service;

import javax.swing.text.html.Option;
import java.util.List;
import java.util.Optional;


@Service
public class TransactionServiceImpl implements  TransactionService{


    public Transaction createTransaction(Transaction transaction) throws DuplicateTransactionException{
        // Assuming you have a repository to interact with the data store
        return null;
    }
    public void deleteTransactionById(String Id) throws TransactionNotFoundException{
        return ;
    }
    public Transaction updateTransaction(Transaction transaction) throws TransactionNotFoundException{
        return null;
    }
    public Transaction getTransactionById(String Id) throws TransactionNotFoundException{
//        Optional<Transaction> trans = ...
//        trans.orElseThrow(new TransactionNotFoundException("not found"));
        return null;
    }
    public List<Transaction> getAllTransactions(){
        // how to make it pass like python
        return null;
    }



}
