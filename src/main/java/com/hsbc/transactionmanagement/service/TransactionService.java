package com.hsbc.transactionmanagement.service;

import com.hsbc.transactionmanagement.exceptions.DuplicateTransactionException;
import com.hsbc.transactionmanagement.exceptions.TransactionNotFoundException;
import com.hsbc.transactionmanagement.model.Transaction;

import java.util.List;
import java.util.Optional;

public interface TransactionService {

    Transaction createTransaction(Transaction transaction) throws DuplicateTransactionException;
    void deleteTransactionById(String Id) throws TransactionNotFoundException;
    Transaction updateTransaction(Transaction transaction) throws TransactionNotFoundException;
    Transaction getTransactionById(String Id) throws TransactionNotFoundException;
    List<Transaction> getAllTransactions();

}
